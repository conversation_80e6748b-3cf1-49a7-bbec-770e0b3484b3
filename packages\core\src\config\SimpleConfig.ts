import { Schema } from "koishi";
import { ModelServiceConfig } from "../adapters";
import { ChatConfig } from "../services/chat";
import { ReplyConditionConfig } from "../middlewares";
import { ToolManagerConfig } from "../extensions";
import { PromptBuilderConfig } from "../types/common";

/**
 * 简化的配置接口
 * 减少嵌套层次，使用更直观的命名
 */
export interface SimpleConfig {
    // 模型配置
    models: ModelServiceConfig;
    chat: ChatConfig;

    // 重试配置（扁平化）
    maxRetries: number;
    retryTimeoutMs: number;
    retryDelayMs: number;
    enableExponentialBackoff: boolean;
    retryableErrors: string[];

    // 适配器切换配置（扁平化）
    enableAdapterSwitching: boolean;
    maxAdapterAttempts: number;

    // 回复条件
    replyCondition: ReplyConditionConfig;

    // 记忆配置（简化）
    memoryBlocks: Record<string, {
        limit: number;
        filePath: string;
    }>;
    memoryModel?: [number, number];

    // 压缩配置（简化）
    compressionTrigger: "lines" | "characters" | "interval";
    compressionThreshold: number;
    compressionPrompt?: string;

    // 工具配置
    tools: ToolManagerConfig;
    maxToolRetries: number;
    toolLifetime: number;

    // 提示词配置
    prompts: PromptBuilderConfig;

    // 调试配置
    enableDebug: boolean;
    uploadErrorDumps: boolean;

    // 聊天配置
    maxHeartbeats: number;
    typingSpeed: number;
}

/**
 * 配置验证器
 */
export class ConfigValidator {
    static validate(config: Partial<SimpleConfig>): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        // 验证重试配置
        if (config.maxRetries !== undefined && (config.maxRetries < 0 || config.maxRetries > 10)) {
            errors.push("maxRetries 必须在 0-10 之间");
        }

        if (config.retryTimeoutMs !== undefined && config.retryTimeoutMs < 1000) {
            errors.push("retryTimeoutMs 不能小于 1000ms");
        }

        // 验证适配器配置
        if (config.maxAdapterAttempts !== undefined && (config.maxAdapterAttempts < 1 || config.maxAdapterAttempts > 5)) {
            errors.push("maxAdapterAttempts 必须在 1-5 之间");
        }

        // 验证记忆块配置
        if (config.memoryBlocks) {
            for (const [name, block] of Object.entries(config.memoryBlocks)) {
                if (block.limit < 0) {
                    errors.push(`记忆块 ${name} 的 limit 不能为负数`);
                }
                if (!block.filePath) {
                    errors.push(`记忆块 ${name} 必须指定 filePath`);
                }
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

/**
 * 配置转换器
 * 将简化配置转换为原始配置格式（用于向后兼容）
 */
export class ConfigTransformer {
    static toOriginalFormat(simpleConfig: SimpleConfig): any {
        return {
            ModelServiceConfig: simpleConfig.models,
            Chat: simpleConfig.chat,
            LLM: {
                RetryConfig: {
                    MaxRetries: simpleConfig.maxRetries,
                    TimeoutMs: simpleConfig.retryTimeoutMs,
                    RetryDelayMs: simpleConfig.retryDelayMs,
                    ExponentialBackoff: simpleConfig.enableExponentialBackoff,
                    RetryableErrors: simpleConfig.retryableErrors,
                },
                AdapterSwitching: {
                    Enabled: simpleConfig.enableAdapterSwitching,
                    MaxAttempts: simpleConfig.maxAdapterAttempts,
                },
            },
            ReplyCondition: simpleConfig.replyCondition,
            Memory: {
                Block: Object.fromEntries(
                    Object.entries(simpleConfig.memoryBlocks).map(([name, block]) => [
                        name,
                        { Limit: block.limit, FilePathToBind: block.filePath }
                    ])
                ),
                UseModel: simpleConfig.memoryModel,
                Compression: {
                    CompressionWhen: simpleConfig.compressionTrigger,
                    [simpleConfig.compressionTrigger === "lines" ? "Lines" :
                      simpleConfig.compressionTrigger === "characters" ? "Characters" : "IntervalMinutes"]:
                      simpleConfig.compressionThreshold,
                    CustomPrompt: simpleConfig.compressionPrompt,
                },
            },
            ToolManagerConfig: simpleConfig.tools,
            ToolCall: {
                MaxRetry: simpleConfig.maxToolRetries,
                Life: simpleConfig.toolLifetime,
            },
            PromptTemplate: simpleConfig.prompts,
            Debug: {
                EnableDebug: simpleConfig.enableDebug,
                UploadDump: simpleConfig.uploadErrorDumps,
            },
        };
    }

    static fromOriginalFormat(originalConfig: any): SimpleConfig {
        return {
            models: originalConfig.ModelServiceConfig,
            chat: originalConfig.Chat,
            maxRetries: originalConfig.LLM?.RetryConfig?.MaxRetries ?? 3,
            retryTimeoutMs: originalConfig.LLM?.RetryConfig?.TimeoutMs ?? 30000,
            retryDelayMs: originalConfig.LLM?.RetryConfig?.RetryDelayMs ?? 1000,
            enableExponentialBackoff: originalConfig.LLM?.RetryConfig?.ExponentialBackoff ?? true,
            retryableErrors: originalConfig.LLM?.RetryConfig?.RetryableErrors ?? [],
            enableAdapterSwitching: originalConfig.LLM?.AdapterSwitching?.Enabled ?? false,
            maxAdapterAttempts: originalConfig.LLM?.AdapterSwitching?.MaxAttempts ?? 3,
            replyCondition: originalConfig.ReplyCondition,
            memoryBlocks: Object.fromEntries(
                Object.entries(originalConfig.Memory?.Block ?? {}).map(([name, block]: [string, any]) => [
                    name,
                    { limit: block.Limit, filePath: block.FilePathToBind }
                ])
            ),
            memoryModel: originalConfig.Memory?.UseModel,
            compressionTrigger: originalConfig.Memory?.Compression?.CompressionWhen ?? "lines",
            compressionThreshold: originalConfig.Memory?.Compression?.Lines ??
                                originalConfig.Memory?.Compression?.Characters ??
                                originalConfig.Memory?.Compression?.IntervalMinutes ?? 100,
            compressionPrompt: originalConfig.Memory?.Compression?.CustomPrompt,
            tools: originalConfig.ToolManagerConfig,
            maxToolRetries: originalConfig.ToolCall?.MaxRetry ?? 3,
            toolLifetime: originalConfig.ToolCall?.Life ?? 3,
            prompts: originalConfig.PromptTemplate,
            enableDebug: originalConfig.Debug?.EnableDebug ?? false,
            uploadErrorDumps: originalConfig.Debug?.UploadDump ?? false,
            maxHeartbeats: originalConfig.Chat?.MaxHeartbeat ?? 2,
            typingSpeed: originalConfig.Chat?.WordsPerSecond ?? 20,
        };
    }
}
