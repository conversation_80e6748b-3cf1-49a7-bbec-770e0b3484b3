import { Schema } from "koishi";
import { ToolManagerConfig, ToolRegistrationOptions } from "./types";

export const ToolManagerConfigSchema: Schema<ToolManagerConfig> = Schema.object({
    autoLoad: Schema.boolean().default(true).description("是否自动加载扩展"),
    extensionPaths: Schema.array(Schema.string()).description("扩展搜索路径列表"),
    logLevel: Schema.union(["debug", "info", "warn", "error"]).default("info").description("日志级别"),
    enableMetrics: Schema.boolean().default(false).description("是否启用性能指标"),
    maxRetries: Schema.number().min(0).max(10).default(3).description("工具执行最大重试次数"),
    timeout: Schema.number().min(1000).max(60000).default(30000).description("工具执行超时时间（毫秒）"),
    hotReload: Schema.boolean().default(false).description("是否启用热重载"),
    validateTypes: Schema.boolean().default(true).description("是否启用类型验证"),
}).description("工具管理器配置");

export const ToolRegistrationOptionsSchema: Schema<ToolRegistrationOptions> = Schema.object({
    replace: Schema.boolean().default(false).description("是否替换已存在的工具"),
    enableHooks: Schema.boolean().default(true).description("是否启用工具钩子"),
}).description("工具注册选项");
