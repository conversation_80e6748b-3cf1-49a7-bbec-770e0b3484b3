import { Context } from "koishi";
import { v4 as uuidv4 } from "uuid";
import { PreprocessingMiddleware, middleware, validateConfig } from "../core/BaseMiddleware";
import { MiddlewareContext, MiddlewarePriority } from "../core/MiddlewareCore";
import { ErrorHandlingConfig } from "../registry/BuiltinMiddlewares";

/**
 * 错误报告上下文
 */
interface ErrorReport {
    errorId: string;
    timestamp: string;
    error: {
        name: string;
        message: string;
        stack?: string;
    };
    context: {
        userId?: string;
        channelId?: string;
        platform?: string;
        messageContent?: string;
        currentPhase: string;
        executedMiddlewares: string[];
    };
    system: {
        nodeVersion: string;
        platform: string;
        arch: string;
        memoryUsage: NodeJS.MemoryUsage;
    };
    performance: Record<string, number>;
}

/**
 * 高级错误处理中间件
 * 职责：捕获和处理中间件链中的所有错误
 */
@middleware({
    id: "builtin.error-handling",
    name: "错误处理中间件",
    phase: "preprocessing" as any,
    priority: MiddlewarePriority.HIGHEST,
})
@validateConfig<ErrorHandlingConfig>((config) => {
    if (config.pasteServiceUrl && !isValidUrl(config.pasteServiceUrl)) {
        return "pasteServiceUrl 必须是有效的URL";
    }
    return true;
})
export class ErrorHandlingMiddleware extends PreprocessingMiddleware<ErrorHandlingConfig> {
    private errorCount = 0;
    private lastErrorTime = 0;
    private readonly maxErrorsPerMinute = 10;

    constructor(ctx: Context, config: ErrorHandlingConfig) {
        super(ctx, config);
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        const startTime = Date.now();

        try {
            await next();
        } catch (error) {
            await this.handleError(error as Error, ctx);
            // 不重新抛出错误，避免影响 Koishi 主流程
        } finally {
            this.recordMetric(ctx, "execution_time", Date.now() - startTime);
        }
    }

    /**
     * 处理错误
     */
    private async handleError(error: Error, ctx: MiddlewareContext): Promise<void> {
        // 错误频率限制
        if (!this.checkErrorRate()) {
            this.logger.warn("错误频率过高，跳过详细处理");
            return;
        }

        const errorId = uuidv4();
        const errorReport = this.createErrorReport(errorId, error, ctx);

        // 记录基础错误信息
        this.logError(errorReport);

        // 可选的错误上传
        if (this.config.uploadDump) {
            await this.uploadErrorReport(errorReport);
        }

        // 更新错误统计
        this.updateErrorStats();

        // 设置共享数据，供其他中间件使用
        this.setShared(ctx, "lastError", {
            id: errorId,
            type: error.name,
            message: error.message,
            timestamp: Date.now(),
        });
    }

    /**
     * 检查错误频率
     */
    private checkErrorRate(): boolean {
        const now = Date.now();
        const oneMinute = 60 * 1000;

        if (now - this.lastErrorTime > oneMinute) {
            this.errorCount = 0;
            this.lastErrorTime = now;
        }

        return this.errorCount < this.maxErrorsPerMinute;
    }

    /**
     * 创建错误报告
     */
    private createErrorReport(errorId: string, error: Error, ctx: MiddlewareContext): ErrorReport {
        return {
            errorId,
            timestamp: new Date().toISOString(),
            error: {
                name: error.name,
                message: error.message,
                stack: this.config.debug ? error.stack : undefined,
            },
            context: {
                userId: ctx.koishiSession?.userId,
                channelId: ctx.koishiSession?.channelId,
                platform: ctx.koishiSession?.platform,
                messageContent: this.config.includeFullSessionContent
                    ? ctx.koishiSession?.content
                    : ctx.koishiSession?.content?.substring(0, 100),
                currentPhase: ctx.metadata.currentPhase,
                executedMiddlewares: [...ctx.metadata.executedMiddlewares],
            },
            system: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                memoryUsage: process.memoryUsage(),
            },
            performance: ctx.getPerformanceMetrics(),
        };
    }

    /**
     * 记录错误日志
     */
    private logError(report: ErrorReport): void {
        this.logger.error(`[${report.errorId}] 中间件执行错误`);
        this.logger.error(`错误类型: ${report.error.name}`);
        this.logger.error(`错误消息: ${report.error.message}`);

        if (this.config.debug && report.error.stack) {
            this.logger.error(`错误堆栈:\n${report.error.stack}`);
        }

        if (report.context.userId) {
            this.logger.error(`触发用户: ${report.context.userId} (${report.context.platform}) ` + `频道: ${report.context.channelId}`);
        }

        this.logger.error(`当前阶段: ${report.context.currentPhase}`);
        this.logger.error(`已执行中间件: ${report.context.executedMiddlewares.join(", ")}`);
    }

    /**
     * 上传错误报告
     */
    private async uploadErrorReport(report: ErrorReport): Promise<void> {
        if (!this.config.pasteServiceUrl) return;

        try {
            const response = await fetch(this.config.pasteServiceUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "User-Agent": "YesImBot-ErrorReporter/2.0",
                },
                body: JSON.stringify(report, null, 2),
                signal: AbortSignal.timeout(10000), // 10秒超时
            });

            if (response.ok) {
                const result = await response.text();
                this.logger.info(`[${report.errorId}] 错误报告已上传: ${result}`);
            } else {
                this.logger.warn(`[${report.errorId}] 错误报告上传失败: ${response.status}`);
            }
        } catch (uploadError) {
            this.logger.warn(`[${report.errorId}] 上传错误报告时发生异常:`, uploadError);
        }
    }

    /**
     * 更新错误统计
     */
    private updateErrorStats(): void {
        this.errorCount++;
        this.lastErrorTime = Date.now();
    }

    /**
     * 健康检查
     */
    async healthCheck(): Promise<boolean> {
        // 检查错误频率是否正常
        const now = Date.now();
        const oneMinute = 60 * 1000;

        if (now - this.lastErrorTime < oneMinute && this.errorCount >= this.maxErrorsPerMinute) {
            return false;
        }

        return true;
    }

    /**
     * 获取错误统计信息
     */
    getErrorStats(): {
        totalErrors: number;
        lastErrorTime: number;
        isHealthy: boolean;
    } {
        return {
            totalErrors: this.errorCount,
            lastErrorTime: this.lastErrorTime,
            isHealthy: this.errorCount < this.maxErrorsPerMinute,
        };
    }
}

/**
 * URL验证辅助函数
 */
function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}
