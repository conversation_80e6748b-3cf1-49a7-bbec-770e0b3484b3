import { Schema } from "koishi";
import { MemoryServiceConfig, CoreMemoryBlockConfig } from "./types";

export const CoreMemoryBlockConfigSchema: Schema<CoreMemoryBlockConfig> = Schema.object({
    Limit: Schema.number().min(0).default(5000).description("记忆块长度限制"),
    InitialValue: Schema.array(Schema.string()).description("初始值列表"),
    FilePathToBind: Schema.path({
        allowCreate: true,
        filters: ["directory", { name: "text", extensions: ["txt"] }],
    }).description("绑定的文件路径"),
}).description("核心记忆块配置");

export const MemoryServiceConfigSchema: Schema<MemoryServiceConfig> = Schema.object({
    CoreBlockDefaults: Schema.dict(CoreMemoryBlockConfigSchema)
        .role("table")
        .default({
            human: { Limit: 5000, FilePathToBind: "data/yesimbot/memory/human.txt" },
            persona: { Limit: 2000, FilePathToBind: "data/yesimbot/memory/persona.txt" },
        })
        .description("核心记忆块默认配置"),
    
    Compression: Schema.object({
        CompressionWhen: Schema.union(["Lines", "Characters", "IntervalMessages", "IntervalMinutes"])
            .default("Lines")
            .description("压缩触发条件"),
        Lines: Schema.number().min(1).default(100).description("按行数触发压缩的阈值"),
        Characters: Schema.number().min(1).default(5000).description("按字符数触发压缩的阈值"),
        IntervalMessages: Schema.number().min(1).default(50).description("按消息间隔触发压缩的阈值"),
        IntervalMinutes: Schema.number().min(1).default(30).description("按时间间隔触发压缩的阈值（分钟）"),
        CustomPrompt: Schema.string().description("自定义压缩提示词"),
        CompressibleBlocks: Schema.array(Schema.string()).description("可压缩的记忆块列表"),
    }).description("记忆压缩配置"),
    
    Backup: Schema.object({
        Enabled: Schema.boolean().default(true).description("是否启用备份"),
        BackupPath: Schema.path({ allowCreate: true }).default("data/yesimbot/backup").description("备份路径"),
    }).description("记忆备份配置"),
    
    UseModel: Schema.object({
        providerName: Schema.string().description("提供商名称"),
        modelId: Schema.string().description("模型ID"),
    }).description("记忆处理使用的模型"),
}).description("记忆服务配置");
