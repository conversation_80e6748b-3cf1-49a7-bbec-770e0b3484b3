import { MESSAGE_TABLE, MEMORY_TABLE, INTERACTION_TABLE, LAST_REPLY_TABLE, IMAGE_TABLE } from "../shared";
import { ChatMessage, MemoryBlockData, Interaction, ImageData } from "../shared/database";

declare module "koishi" {
    interface Tables {
        [MESSAGE_TABLE]: ChatMessage;
        [MEMORY_TABLE]: MemoryBlockData;
        [INTERACTION_TABLE]: Interaction;
        [LAST_REPLY_TABLE]: {
            channelId: string;
            timestamp: Date;
        };
        [IMAGE_TABLE]: ImageData;
    }
}

export * from "./adapters";
export * from "./memory";
