import { Context } from "koishi";
import { OutputProcessingMiddleware, middleware, validateConfig } from "../core/BaseMiddleware";
import { MiddlewareContext, MiddlewarePriority } from "../core/MiddlewareCore";
import { ConversationState, MiddlewareManager } from "../base";
import { ResponseHandlingConfig } from "../registry/BuiltinMiddlewares";
import { OutputFormat, FunctionTool } from "../../types/middleware";

/**
 * 工具执行结果
 */
interface ToolExecutionResult {
    status: "success" | "error" | "timeout";
    result?: any;
    error?: string;
    duration: number;
}

/**
 * 工具验证器
 */
class ToolValidator {
    constructor(private logger: any) {}

    validateTool(tool: FunctionTool): boolean {
        if (!tool.function || typeof tool.function !== "string") {
            this.logger.warn(`工具验证失败: 无效的函数名 ${tool.function}`);
            return false;
        }

        if (!tool.params || typeof tool.params !== "object") {
            this.logger.warn(`工具验证失败: 无效的参数 ${tool.function}`);
            return false;
        }

        return true;
    }

    validateParams(tool: FunctionTool, expectedSchema?: any): boolean {
        // 这里可以实现更复杂的参数验证逻辑
        // 例如：JSON Schema 验证
        return true;
    }
}

/**
 * 工具执行器
 */
class ToolExecutor {
    constructor(private ctx: Context, private config: ResponseHandlingConfig, private logger: any) {}

    async executeTool(tool: FunctionTool, ctx: MiddlewareContext): Promise<ToolExecutionResult> {
        const startTime = Date.now();

        try {
            const toolManager = this.ctx["yesimbot.tool"];
            if (!toolManager) {
                throw new Error("工具管理器未找到");
            }

            const toolInstance = toolManager.getTool(tool.function);
            if (!toolInstance) {
                throw new Error(`工具 ${tool.function} 未找到`);
            }

            // 执行工具
            const result = await this.executeWithRetry(toolInstance, tool.params, ctx);

            return {
                status: "success",
                result: result.result,
                duration: Date.now() - startTime,
            };
        } catch (error) {
            return {
                status: "error",
                error: (error as Error).message,
                duration: Date.now() - startTime,
            };
        }
    }

    private async executeWithRetry(toolInstance: any, params: Record<string, unknown>, ctx: MiddlewareContext): Promise<any> {
        let lastResult: any = null;

        for (let attempt = 1; attempt <= this.config.maxRetry + 1; attempt++) {
            try {
                if (attempt > 1) {
                    this.logger.info(`工具重试 ${attempt - 1}/${this.config.maxRetry}`);
                    await this.sleep(1500); // 重试延迟
                }

                lastResult = await toolInstance.execute(params, {
                    koishiContext: ctx.koishiContext,
                    koishiSession: ctx.koishiSession,
                    platform: ctx.platform,
                });

                if (lastResult.status === "success") {
                    return lastResult;
                }
            } catch (error) {
                this.logger.error(`工具执行失败 (尝试 ${attempt}):`, error);
                if (attempt === this.config.maxRetry + 1) {
                    throw error;
                }
            }
        }

        return lastResult || { status: "error", result: "工具执行失败" };
    }

    private sleep(ms: number): Promise<void> {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
}

/**
 * 高级响应处理中间件
 * 职责：处理LLM响应，包括工具调用和心跳机制
 */
@middleware({
    id: "builtin.response-handling",
    name: "响应处理中间件",
    phase: "output_processing" as any,
    priority: MiddlewarePriority.HIGH,
    dependencies: ["builtin.llm-processing"],
})
@validateConfig<ResponseHandlingConfig>((config) => {
    if (config.maxRetry < 0 || config.maxRetry > 10) {
        return "maxRetry 必须在 0-10 之间";
    }
    if (config.life < 1 || config.life > 10) {
        return "life 必须在 1-10 之间";
    }
    return true;
})
export class ResponseHandlingMiddleware extends OutputProcessingMiddleware<ResponseHandlingConfig> {
    private toolValidator: ToolValidator;
    private toolExecutor: ToolExecutor;

    constructor(ctx: Context, config: ResponseHandlingConfig) {
        super(ctx, config);

        this.toolValidator = new ToolValidator(this.logger);
        this.toolExecutor = new ToolExecutor(ctx, config, this.logger);
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        // 只在响应状态下执行
        if (ctx.state !== ConversationState.RESPONDING) {
            await next();
            return;
        }

        const startTime = Date.now();

        try {
            // 1. 解析和验证响应
            const response = this.parseAndValidateResponse(ctx);
            if (!response) {
                this.logger.warn("LLM响应解析失败或无效，处理中止");
                await this.finalizeProcessing(ctx);
                return;
            }

            // 2. 记录思考过程
            this.logThoughts(response.thoughts);

            // 3. 处理动作和构建代理响应
            const agentResponse = await this.processActions(ctx, response.actions, response.thoughts);
            ctx.agentResponses.push(agentResponse);

            // 4. 保存代理响应到数据库
            await this.saveAgentResponse(ctx, agentResponse);

            // 5. 处理心跳请求
            if (response.request_heartbeat) {
                await this.handleHeartbeat(ctx);
            } else {
                await next();
            }
        } catch (error) {
            this.logger.error("处理LLM响应时发生错误:", error);
            throw error;
        } finally {
            await this.finalizeProcessing(ctx);
            this.recordMetric(ctx, "execution_time", Date.now() - startTime);
        }
    }

    /**
     * 解析和验证响应
     */
    private parseAndValidateResponse(ctx: MiddlewareContext): OutputFormat | null {
        try {
            if (!ctx.llmResponse?.text) {
                this.logger.warn("LLM响应为空");
                return null;
            }

            const response = JSON.parse(ctx.llmResponse.text) as OutputFormat;

            // 验证响应结构
            if (!response.thoughts || !response.actions || typeof response.request_heartbeat !== "boolean") {
                this.logger.warn("LLM响应结构无效");
                return null;
            }

            return response;
        } catch (error) {
            this.logger.error("解析LLM响应失败:", error);
            return null;
        }
    }

    /**
     * 记录思考过程
     */
    private logThoughts(thoughts: OutputFormat["thoughts"]): void {
        if (this.config.enableToolValidation) {
            this.logger.debug("=== AI思考过程 ===");
            this.logger.debug(`观察: ${thoughts.observe}`);
            this.logger.debug(`分析推理: ${thoughts.analyze_infer}`);
            this.logger.debug(`计划: ${thoughts.plan}`);
            this.logger.debug("================");
        }
    }

    /**
     * 处理动作
     */
    private async processActions(ctx: MiddlewareContext, actions: FunctionTool[], thoughts: OutputFormat["thoughts"]): Promise<any> {
        const actionResults: any[] = [];
        const executionPromises: Promise<void>[] = [];

        for (const [index, action] of actions.entries()) {
            const executeAction = async () => {
                // 验证工具
                if (this.config.enableToolValidation && !this.toolValidator.validateTool(action)) {
                    actionResults[index] = {
                        status: "error",
                        error: "工具验证失败",
                        duration: 0,
                    };
                    return;
                }

                // 执行工具
                const result = await this.toolExecutor.executeTool(action, ctx);
                actionResults[index] = result;

                this.logger.debug(
                    `工具 ${action.function} 执行${result.status === "success" ? "成功" : "失败"} ` + `(耗时: ${result.duration}ms)`
                );
            };

            if (this.config.parallelExecution) {
                executionPromises.push(executeAction());
            } else {
                await executeAction();
            }
        }

        // 等待并行执行完成
        if (this.config.parallelExecution) {
            await Promise.all(executionPromises);
        }

        // 构建代理响应对象
        return {
            thoughts,
            actions: actions.map((action, index) => ({
                function: action.function,
                params: action.params,
                result: actionResults[index],
            })),
            timestamp: new Date().toISOString(),
            executionTime: actionResults.reduce((sum, result) => sum + (result?.duration || 0), 0),
        };
    }

    /**
     * 保存代理响应
     */
    private async saveAgentResponse(ctx: MiddlewareContext, agentResponse: any): Promise<void> {
        try {
            const dataManager = this.ctx["yesimbot.data"];
            if (dataManager && ctx.currentTurnId) {
                await dataManager.addAgentResponse(ctx.currentTurnId, agentResponse);
            }
        } catch (error) {
            this.logger.error("保存代理响应失败:", error);
        }
    }

    /**
     * 处理心跳请求
     */
    private async handleHeartbeat(ctx: MiddlewareContext): Promise<void> {
        ctx.heartbeatCount++;

        if (ctx.heartbeatCount >= this.config.maxHeartbeat) {
            this.logger.info(`达到最大心跳次数 (${this.config.maxHeartbeat})，结束对话`);
            await this.finalizeProcessing(ctx);
            return;
        }

        this.logger.debug(`心跳请求 ${ctx.heartbeatCount}/${this.config.maxHeartbeat}`);

        // 重新执行中间件链（从LLM处理开始）
        const middlewareManager: MiddlewareManager = this.getShared(ctx, "middlewareManager");
        if (middlewareManager) {
            await middlewareManager.executeFrom(ctx, this.findLLMProcessingIndex());
        }
    }

    /**
     * 查找LLM处理中间件的索引
     */
    private findLLMProcessingIndex(): number {
        // 这里需要根据实际的中间件管道实现来查找索引
        // 简化实现，返回固定值
        return 3; // 假设LLM处理中间件在第4个位置
    }

    /**
     * 完成处理
     */
    private async finalizeProcessing(ctx: MiddlewareContext): Promise<void> {
        try {
            await ctx.transitionTo(ConversationState.IDLE);
            this.setShared(ctx, "processingComplete", true);
        } catch (error) {
            this.logger.error("完成处理时发生错误:", error);
        }
    }

    /**
     * 健康检查
     */
    async healthCheck(): Promise<boolean> {
        try {
            const toolManager = this.ctx["yesimbot.tool"];
            return !!toolManager;
        } catch (error) {
            this.logger.error("响应处理健康检查失败:", error);
            return false;
        }
    }
}
