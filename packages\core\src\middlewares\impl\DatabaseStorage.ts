import { Context, Element, h } from "koishi";
import { InputProcessingMiddleware, middleware, validateConfig } from "../core/BaseMiddleware";
import { MiddlewareContext, MiddlewarePriority } from "../core/MiddlewareCore";
import { DatabaseStorageConfig } from "../registry/BuiltinMiddlewares";
import { getChannelType, MESSAGE_TABLE } from "../../shared";
import { ChatMessage } from "../../shared/database";

/**
 * 批处理队列项
 */
interface BatchItem {
    type: "message" | "image" | "worldstate";
    data: any;
    timestamp: number;
}

/**
 * 高级数据库存储中间件
 * 职责：处理消息存储、图片处理和世界状态更新
 */
@middleware({
    id: "builtin.database-storage",
    name: "数据库存储中间件",
    phase: "input_processing" as any,
    priority: MiddlewarePriority.HIGH,
    dependencies: ["builtin.error-handling"],
})
@validateConfig<DatabaseStorageConfig>((config) => {
    if (config.batchSize && (config.batchSize < 1 || config.batchSize > 100)) {
        return "batchSize 必须在 1-100 之间";
    }
    if (config.timeout && config.timeout < 1000) {
        return "timeout 不能小于 1000ms";
    }
    return true;
})
export class DatabaseStorageMiddleware extends InputProcessingMiddleware<DatabaseStorageConfig> {
    private batchQueue: BatchItem[] = [];
    private batchTimer?: NodeJS.Timeout;
    private processingBatch = false;

    constructor(ctx: Context, config: DatabaseStorageConfig) {
        super(ctx, config);
    }

    async initialize(): Promise<void> {
        // 启动批处理定时器
        // this.startBatchTimer();
        this.logger.info("数据库存储中间件初始化完成");
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        const startTime = Date.now();

        try {
            // 1. 处理消息元素
            const processedElements = await this.processMessageElements(ctx);

            // 2. 保存消息到数据库
            const savedMessage = await this.saveMessage(ctx, processedElements);

            // 3. 更新世界状态
            if (savedMessage && this.config.enableWorldStateUpdate) {
                await this.updateWorldState(ctx);
            }

            // 4. 设置共享数据
            this.setShared(ctx, "savedMessage", savedMessage);
            this.setShared(ctx, "processedElements", processedElements);

            await next();
        } finally {
            this.recordMetric(ctx, "execution_time", Date.now() - startTime);
        }
    }

    /**
     * 处理消息元素
     */
    private async processMessageElements(ctx: MiddlewareContext): Promise<Element[]> {
        const elements = ctx.koishiSession.elements;
        const processedElements: Element[] = [];

        for await (const element of elements) {
            switch (element.type) {
                case "text":
                    processedElements.push(element);
                    break;

                case "image":
                case "img":
                    if (this.config.enableImageProcessing) {
                        const processedImage = await this.processImage(element);
                        processedElements.push(processedImage);
                    } else {
                        processedElements.push(element);
                    }
                    break;

                case "at":
                    processedElements.push(element);
                    break;

                case "video":
                    processedElements.push(element);
                    break;

                default:
                    processedElements.push(element);
                    break;
            }
        }

        // 处理引用消息
        if (ctx.koishiSession.quote) {
            processedElements.unshift(h.quote(ctx.koishiSession.quote.id));
        }

        return processedElements;
    }

    /**
     * 处理图片元素
     */
    private async processImage(element: Element): Promise<Element> {
        try {
            // 获取图片处理器服务
            const imageProcessor = this.ctx["yesimbot.imageProcessor"];
            if (!imageProcessor) {
                this.logger.warn("图片处理器服务未找到，跳过图片处理");
                return element;
            }

            const imageData = await imageProcessor.process(element);
            if (imageData) {
                return h("img", {
                    id: imageData.id,
                    summary: element.attrs.summary,
                    desc: imageData.desc || null,
                });
            }
        } catch (error) {
            this.logger.error("处理图片时发生错误:", error);
        }

        return element;
    }

    /**
     * 保存消息到数据库
     */
    private async saveMessage(ctx: MiddlewareContext, elements: Element[]): Promise<ChatMessage | null> {
        const session = ctx.koishiSession;
        const content = elements.join("");

        try {
            // 检查消息是否已存在
            const existingMessages = await this.ctx.database.get(MESSAGE_TABLE, {
                messageId: session.messageId,
                channel: { id: session.channelId },
            });

            if (existingMessages.length > 0) {
                this.logger.debug(`消息 ${session.messageId} 已存在，跳过保存`);
                return existingMessages[0];
            }

            // 创建新消息记录
            const messageData: Partial<ChatMessage> = {
                messageId: session.messageId,
                content,
                timestamp: new Date(session.timestamp),
                channel: {
                    id: session.channelId,
                    // name: session.channelName || "",
                    type: getChannelType(session.channelId),
                },
                sender: {
                    id: session.userId,
                    name: session.username || "",
                    // avatar: session.user?.avatar || "",
                },
                platform: session.platform,
                elements: elements.map((el) => ({
                    type: el.type,
                    attrs: el.attrs || {},
                })),
            };

            // 批量保存或立即保存
            // if (this.config.batchSize && this.config.batchSize > 1) {
            //     this.addToBatch("message", messageData);
            //     return null; // 批量模式下不立即返回
            // } else {
            const savedMessage = await this.ctx.database.create(MESSAGE_TABLE, messageData);
            this.logger.debug(`消息 ${session.messageId} 保存成功`);
            return savedMessage;
            // }
        } catch (error) {
            this.logger.error("保存消息时发生错误:", error);
            return null;
        }
    }

    /**
     * 更新世界状态
     */
    private async updateWorldState(ctx: MiddlewareContext): Promise<void> {
        try {
            const dataManager = this.ctx["yesimbot.data"];
            if (!dataManager) {
                this.logger.warn("数据管理器服务未找到，跳过世界状态更新");
                return;
            }

            if (ctx.currentTurnId) {
                await dataManager.touchChannel(ctx.koishiSession);
                await dataManager.addMessageEvent(ctx.currentTurnId, ctx.koishiSession);
            }
        } catch (error) {
            this.logger.error("更新世界状态时发生错误:", error);
        }
    }

    /**
     * 添加到批处理队列
     */
    // private addToBatch(type: BatchItem["type"], data: any): void {
    //     this.batchQueue.push({
    //         type,
    //         data,
    //         timestamp: Date.now(),
    //     });

    //     if (this.batchQueue.length >= (this.config.batchSize || 10)) {
    //         this.processBatch();
    //     }
    // }

    /**
     * 启动批处理定时器
     */
    // private startBatchTimer(): void {
    //     const interval = this.config.timeout || 5000;
    //     this.batchTimer = setInterval(() => {
    //         if (this.batchQueue.length > 0) {
    //             this.processBatch();
    //         }
    //     }, interval);
    // }

    /**
     * 处理批处理队列
     */
    // private async processBatch(): Promise<void> {
    //     if (this.processingBatch || this.batchQueue.length === 0) return;

    //     this.processingBatch = true;
    //     const items = this.batchQueue.splice(0);

    //     try {
    //         const messageItems = items.filter((item) => item.type === "message");

    //         if (messageItems.length > 0) {
    //             await this.ctx.database.create(
    //                 MESSAGE_TABLE,
    //                 messageItems.map((item) => item.data)
    //             );
    //             this.logger.debug(`批量保存 ${messageItems.length} 条消息`);
    //         }
    //     } catch (error) {
    //         this.logger.error("批处理时发生错误:", error);
    //     } finally {
    //         this.processingBatch = false;
    //     }
    // }

    /**
     * 获取频道类型
     */
    private getChannelType(session: any): string {
        // 这里可以根据实际需求实现频道类型判断逻辑
        return session.isDirect ? "direct" : "group";
    }

    /**
     * 清理资源
     */
    async dispose(): Promise<void> {
        if (this.batchTimer) {
            clearInterval(this.batchTimer);
            this.batchTimer = undefined;
        }

        // 处理剩余的批处理项
        // if (this.batchQueue.length > 0) {
        //     await this.processBatch();
        // }

        this.logger.info("数据库存储中间件已清理");
    }

    /**
     * 健康检查
     */
    async healthCheck(): Promise<boolean> {
        try {
            // 检查数据库连接
            await this.ctx.database.get(MESSAGE_TABLE, {}, { limit: 1 });
            return true;
        } catch (error) {
            this.logger.error("数据库健康检查失败:", error);
            return false;
        }
    }
}
