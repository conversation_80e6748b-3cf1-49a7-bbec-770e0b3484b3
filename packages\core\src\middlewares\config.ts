import { Schema } from "koishi";
import { ReplyConditionConfig, ErrorHandlingOptions, RetryConfig, AdapterSwitchingConfig } from "./types";

export const ReplyConditionConfigSchema: Schema<ReplyConditionConfig> = Schema.object({
    Channels: Schema.array(Schema.array(String).role("table")).description("允许回复的频道列表"),
    TestMode: Schema.boolean().default(false).description("测试模式，每条消息都会触发回复"),

    Strategies: Schema.object({
        AtMention: Schema.object({
            Enabled: Schema.boolean().default(true).description("是否启用@提及回复"),
            Probability: Schema.number().min(0).max(1).default(0.8).step(0.1).role("slider").description("@提及时的回复概率"),
        }).description("@提及策略"),

        Threshold: Schema.object({
            Enabled: Schema.boolean().default(true).description("是否启用阈值策略"),
            Value: Schema.number().min(0).max(100).default(50).step(1).role("slider").description("回复阈值"),
        }).description("阈值策略"),

        ConversationFlow: Schema.object({
            Enabled: Schema.boolean().default(false).description("是否启用对话流分析"),
            ConfidenceThreshold: Schema.number().min(0).max(1).default(0.7).step(0.1).role("slider").description("对话流置信度阈值"),
        }).description("对话流策略"),
    }).description("回复策略配置"),

    Timing: Schema.object({
        WaitTime: Schema.number().min(0).max(10000).default(1000).step(100).description("等待时间（毫秒）"),
        SameUserThreshold: Schema.number().min(0).max(60000).default(5000).step(1000).description("同用户消息间隔阈值（毫秒）"),
    }).description("时间控制配置"),

    Advanced: Schema.object({
        Willingness: Schema.object({
            MessageIncrease: Schema.number().default(10).min(0).max(50).description("收到普通消息时增加的意愿值"),
            AtIncrease: Schema.number().default(30).min(0).max(100).description("收到 @ 消息时增加的意愿值"),
            DecayRate: Schema.number().default(2).min(0).max(20).description("意愿值每分钟衰减量"),
            RetentionAfterReply: Schema.number()
                .default(0.3)
                .min(0)
                .max(1)
                .step(0.1)
                .role("slider")
                .description("回复后保留的意愿值比例"),
            Keywords: Schema.object({
                List: Schema.array(String).description("关键词列表").collapse(),
                Increase: Schema.number().default(10).min(0).max(100).description("额外增加的意愿值"),
            }).description("特定关键词配置"),
        }).description("意愿值系统配置"),
    })
        .description("高级功能配置")
        .collapse(),
}).description("回复条件配置");

export const ErrorHandlingOptionsSchema: Schema<ErrorHandlingOptions> = Schema.object({
    debug: Schema.boolean().default(false).description("是否开启调试模式"),
    uploadDump: Schema.boolean().default(false).description("是否上传错误倾倒"),
    pasteServiceUrl: Schema.string().description("粘贴服务URL"),
    includeFullSessionContent: Schema.boolean().default(false).description("是否包含完整会话内容"),
}).description("错误处理选项");

export const RetryConfigSchema: Schema<RetryConfig> = Schema.object({
    maxRetries: Schema.number().min(0).max(10).default(3).description("最大重试次数"),
    timeoutMs: Schema.number().min(1000).max(60000).default(30000).description("超时时间（毫秒）"),
    retryDelayMs: Schema.number().min(100).max(10000).default(1000).description("重试延迟（毫秒）"),
    exponentialBackoff: Schema.boolean().default(true).description("是否启用指数退避"),
    retryableErrors: Schema.array(String).description("可重试的错误类型列表"),
}).description("重试配置");

export const AdapterSwitchingConfigSchema: Schema<AdapterSwitchingConfig> = Schema.object({
    enabled: Schema.boolean().default(false).description("是否启用适配器切换"),
    maxAttempts: Schema.number().min(1).max(10).default(3).description("适配器切换的最大尝试次数"),
}).description("适配器切换配置");
