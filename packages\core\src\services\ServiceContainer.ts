import { SERVICE_TOKENS, ServiceToken, IServiceContainer } from "./types";

/**
 * 服务容器实现
 */
export class ServiceContainer implements IServiceContainer {
    private services = new Map<ServiceToken, any>();
    private factories = new Map<ServiceToken, () => any>();

    register<T>(token: ServiceToken, factory: () => T): void {
        this.factories.set(token, factory);
    }

    get<T>(token: ServiceToken): T {
        if (!this.services.has(token)) {
            const factory = this.factories.get(token);
            if (!factory) {
                throw new Error(`服务未注册: ${token.toString()}`);
            }
            this.services.set(token, factory());
        }
        return this.services.get(token);
    }

    has(token: ServiceToken): boolean {
        return this.factories.has(token);
    }

    /**
     * 清理所有服务
     */
    dispose(): void {
        this.services.clear();
        this.factories.clear();
    }
}
