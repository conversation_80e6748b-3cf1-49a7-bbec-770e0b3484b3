import { Schema } from "koishi";
import { PlatformAdapterConfig } from "./types";

export const PlatformAdapterConfigSchema: Schema<PlatformAdapterConfig> = Schema.object({
    enableUserInfoCache: Schema.boolean().default(true).description("是否启用用户信息缓存"),
    enableGroupInfoCache: Schema.boolean().default(true).description("是否启用群组信息缓存"),
    cacheExpireTime: Schema.number().min(60).max(86400).default(3600).description("缓存过期时间（秒）"),
}).description("平台适配器配置");
