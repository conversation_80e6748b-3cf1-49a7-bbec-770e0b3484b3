/**
 * 服务标识符
 */
export const SERVICE_TOKENS = {
    CHAT_MODEL_SWITCHER: Symbol("ChatModelSwitcher"),
    IMAGE_PROCESSOR: Symbol("ImageProcessor"),
    DATA_MANAGER: Symbol("DataManager"),
    PROMPT_BUILDER: Symbol("PromptBuilder"),
    MIDDLEWARE_MANAGER: Symbol("MiddlewareManager"),
    TOOL_MANAGER: Symbol("ToolManager"),
    MEMORY_SERVICE: Symbol("MemoryService"),
    MODEL_SERVICE: Symbol("ModelService"),
} as const;

export type ServiceToken = (typeof SERVICE_TOKENS)[keyof typeof SERVICE_TOKENS];

/**
 * 服务容器接口
 */
export interface IServiceContainer {
    register<T>(token: ServiceToken, factory: () => T): void;
    get<T>(token: ServiceToken): T;
    has(token: ServiceToken): boolean;
}

export type GroupRole = "member" | "admin" | "owner";

export interface GroupMemberInfo {
    area?: string;
    level?: string;
    title?: string;
    role?: GroupRole;
    card?: string;
    card_changeable?: boolean;
    group_id: string;
    join_time: number;
    last_sent_time?: number;
    title_expire_time?: number;
    unfriendly?: boolean;
    [key: string]: any;
}

export interface UserInfo {
    userId: string;
    sex?: string;
    nickname?: string;
    avatar?: string;
    sign?: string;
    [key: string]: any;
}

export interface GroupInfo {
    groupId: string;
    name?: string;
    avatar?: string;
    maxMemberCount?: number;
    memberCount?: number;
    [key: string]: any;
}

/**
 * 平台适配器配置
 */
export interface PlatformAdapterConfig {
    enableUserInfoCache?: boolean;
    enableGroupInfoCache?: boolean;
    cacheExpireTime?: number;
}
