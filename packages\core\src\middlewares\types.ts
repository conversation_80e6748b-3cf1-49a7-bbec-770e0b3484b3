import { Context, Session } from "koishi";
import type { GenerateTextResult } from "xsai";

// 回复策略中间件
export interface ReplyConditionConfig {
    // 基础配置
    Channels: string[][];
    TestMode?: boolean;

    // 回复策略配置
    Strategies: {
        AtMention: {
            Enabled: boolean;
            // 暂时不清楚Computed的用法，希望可以针对不同群组，不同用户设定不同的回复概率
            // Probability: number | Computed<number>;
            Probability: number;
        };
        Threshold: {
            Enabled: boolean;
            Value: number;
        };
        ConversationFlow: {
            Enabled: boolean;
            ConfidenceThreshold: number;
        };
    };

    // 时间控制
    Timing: {
        WaitTime: number;
        SameUserThreshold: number;
    };

    // 高级功能（可选）
    Advanced?: {
        Willingness?: {
            MessageIncrease: number;
            AtIncrease: number;
            DecayRate: number;
            RetentionAfterReply: number;
            Keywords?: {
                List: string[];
                Increase: number;
            };
        };
    };
}

// 1. 优化 ErrorContext 接口，增加原始错误对象和更多上下文
export interface ErrorReportContext {
    originalError: Error; // 包含原始错误对象，方便提取更多信息
    llmResponse?: GenerateTextResult;
    koishiContext?: Context; // Koishi 上下文对象
    koishiSession?: Session; // Koishi 会话对象
    additionalInfo?: Record<string, any>; // 额外的自定义信息
    errorId?: string; // 错误唯一ID
}

export interface ErrorHandlingOptions {
    debug?: boolean; // 是否开启调试模式，打印详细堆栈
    uploadDump?: boolean; // 是否上传错误倾倒
    pasteServiceUrl?: string; // 粘贴服务的URL
    // 敏感信息处理选项，例如是否包含完整的会话内容
    includeFullSessionContent?: boolean;
}

/**
 * 重试配置
 */
export interface RetryConfig {
    maxRetries: number;
    timeoutMs: number;
    retryDelayMs: number;
    exponentialBackoff: boolean;
    retryableErrors: string[];
}

/**
 * 适配器切换配置
 */
export interface AdapterSwitchingConfig {
    enabled: boolean;
    maxAttempts: number;
}

export interface FunctionTool {
    function: string;
    params: Record<string, unknown>;
}

export interface OutputFormat {
    thoughts: {
        observe: string;
        analyze_infer: string;
        plan: string;
    };
    actions: FunctionTool[];
    request_heartbeat: boolean;
}
