import { ModelDescriptor } from "../adapters";

export interface CoreMemoryBlockConfig {
    Limit?: number;
    InitialValue?: string[];
    FilePathToBind?: string;
}

export interface MemoryBlockCompressionState {
    MessageCount: number;
    LastCompressionTime: Date;
}

export interface MemoryServiceConfig {
    CoreBlockDefaults?: {
        persona?: CoreMemoryBlockConfig;
        human?: CoreMemoryBlockConfig;
        [key: string]: CoreMemoryBlockConfig;
    };
    Compression?: {
        CompressionWhen?: "Lines" | "Characters" | "IntervalMessages" | "IntervalMinutes";
        Lines?: number;
        Characters?: number;
        IntervalMessages?: number;
        IntervalMinutes?: number;
        CustomPrompt?: string;
        CompressibleBlocks?: string[];
    };
    Backup?: {
        Enabled: boolean;
        BackupPath: string;
    };
    UseModel?: ModelDescriptor;
}
